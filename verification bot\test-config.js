// ملف اختبار للتأكد من صحة الإعدادات
const config = require('./config.js');

console.log('🔍 فحص إعدادات البوت...\n');

// فحص التوكن
if (config.token === 'YOUR_BOT_TOKEN_HERE') {
    console.log('❌ لم يتم تعيين توكن البوت');
} else {
    console.log('✅ تم تعيين توكن البوت');
}

// فحص الأدوار
console.log('\n📋 معرفات الأدوار:');
console.log(`رول التحقق: ${config.roles.verified}`);
console.log(`رول الذكور: ${config.roles.male}`);
console.log(`رول الإناث: ${config.roles.female}`);
console.log(`رول غير المتحققين: ${config.roles.unverified}`);

// فحص معرف السيرفر
if (config.guildId === 'YOUR_GUILD_ID_HERE') {
    console.log('\n⚠️  لم يتم تعيين معرف السيرفر (سيتم التسجيل العالمي للأوامر)');
} else {
    console.log(`\n✅ معرف السيرفر: ${config.guildId}`);
}

console.log('\n✨ انتهى الفحص!');