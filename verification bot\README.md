# بوت التحقق - Verification Bot

بوت ديسكورد للتحقق من الأعضاء الجدد وإعطائهم الأدوار المناسبة.

## الميزات

- ✅ أمر `/verification` للتحقق من الأعضاء
- 🎭 إعطاء أدوار مختلفة حسب الجنس
- 🗑️ إزالة رول غير المتحققين تلقائياً
- 📝 تغيير اسم العضو
- 💌 رسالة ترحيب خاصة للعضو
- 📊 رسالة تأكيد مفصلة

## التثبيت

1. تأكد من تثبيت Node.js (الإصدار 16.9.0 أو أحدث)
2. قم بتثبيت المكتبات:
```bash
npm install
```

## الإعداد

1. افتح ملف `config.js`
2. ضع توكن البوت في `token`
3. تأكد من معرفات الأدوار في `roles`
4. (اختياري) ضع معرف السيرفر في `guildId` للتسجيل السريع للأوامر

## الاستخدام

### تشغيل البوت
```bash
npm start
```

### الأوامر

#### `/verification`
- **الوصف**: تحقق من عضو جديد
- **المعاملات**:
  - `name`: اسم العضو (مطلوب)
  - `gender`: جنس العضو (ولد/بنت) (مطلوب)
  - `user`: العضو المراد التحقق منه (مطلوب)
- **الصلاحيات المطلوبة**: إدارة الأدوار

### مثال على الاستخدام
```
/verification name:أحمد gender:ولد user:@Ahmed#1234
```

## الأدوار المستخدمة

- **رول التحقق الأساسي**: `1343730255143112758` - يُعطى لجميع الأعضاء المتحققين
- **رول الذكور**: `1343819078577557586` - يُعطى للأولاد
- **رول الإناث**: `1343716203146772603` - يُعطى للبنات
- **رول غير المتحققين**: `1373721764802199623` - يتم إزالته عند التحقق

## المتطلبات

- صلاحيات البوت:
  - إدارة الأدوار (Manage Roles)
  - إدارة الأسماء المستعارة (Manage Nicknames)
  - إرسال الرسائل (Send Messages)
  - استخدام الأوامر المائلة (Use Slash Commands)

## الأمان

- الأمر محمي بصلاحية "إدارة الأدوار"
- التحقق من وجود العضو في السيرفر
- التحقق من وجود الأدوار قبل التطبيق
- معالجة الأخطاء بشكل آمن

## الدعم

إذا واجهت أي مشاكل، تأكد من:
1. صحة توكن البوت
2. وجود الأدوار في السيرفر
3. صلاحيات البوت في السيرفر
4. أن البوت أعلى من الأدوار التي يحاول إدارتها في التسلسل الهرمي