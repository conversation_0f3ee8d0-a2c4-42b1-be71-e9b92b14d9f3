# دليل الإعداد السريع - بوت التحقق

## خطوات التشغيل السريع

### 1. تثبيت المكتبات
```bash
cd "verification bot"
npm install
```

### 2. إ<PERSON><PERSON><PERSON> البوت
1. افت<PERSON> ملف `config.js`
2. است<PERSON><PERSON><PERSON> `YOUR_BOT_TOKEN_HERE` بتوكن البوت الخاص بك
3. (اختياري) استبدل `YOUR_GUILD_ID_HERE` بمعرف السيرفر للتسجيل السريع

### 3. التحقق من الأدوار
تأكد من وجود هذه الأدوار في سيرفرك:
- `1343730255143112758` - رول التحقق الأساسي
- `1343819078577557586` - رول الذكور  
- `1343716203146772603` - رول الإناث
- `1373721764802199623` - رول غير المتحققين (سيتم إزالته)

### 4. صلاحيات البوت
تأكد من أن البوت لديه هذه الصلاحيات:
- ✅ إدارة الأدوار (Manage Roles)
- ✅ إدارة الأسماء المستعارة (Manage Nicknames)  
- ✅ إرسال الرسائل (Send Messages)
- ✅ استخدام الأوامر المائلة (Use Slash Commands)

### 5. تشغيل البوت
```bash
npm start
```
أو انقر مرتين على `start.bat`

## استخدام الأمر

```
/verification name:اسم_العضو gender:ولد user:@العضو
```

### مثال:
```
/verification name:أحمد gender:ولد user:@Ahmed#1234
```

## ملاحظات مهمة

1. **ترتيب الأدوار**: تأكد من أن رول البوت أعلى من الأدوار التي يحاول إدارتها
2. **الصلاحيات**: فقط الأعضاء الذين لديهم صلاحية "إدارة الأدوار" يمكنهم استخدام الأمر
3. **الأمان**: البوت يتحقق من وجود العضو والأدوار قبل التطبيق

## استكشاف الأخطاء

### البوت لا يستجيب للأوامر
- تحقق من صحة التوكن
- تأكد من دعوة البوت للسيرفر بالصلاحيات المطلوبة

### خطأ في إضافة الأدوار
- تحقق من وجود الأدوار في السيرفر
- تأكد من أن رول البوت أعلى من الأدوار المستهدفة

### الأمر غير ظاهر
- انتظر بضع دقائق (الأوامر العالمية تحتاج وقت)
- أو ضع معرف السيرفر في `config.js` للتسجيل السريع